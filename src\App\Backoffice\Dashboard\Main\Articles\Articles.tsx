import React from "react";
import Fade from "../../../../../utilities/minitiatures/Fade/Fade";
import ArticlesList from "./ArticlesList/ArticlesList";
import AddArticle from "./AddArticle/AddArticle";
import DeleteArticle from "./DeleteArticle/DeleteArticle";
import EditArticle from "./EditArticle/EditArticle";
import { refreshCategories, setArticles } from "../../../../../utilities/redux/backoffice/backofficeSlice";
import { AppDispatch, Rootstate } from "../../../../../utilities/redux/store";
import { useDispatch, useSelector } from "react-redux";
import { Article } from "../../../../../utilities/constants/types";
import ArticlesEmpty from "./ArticlesEmpty/ArticlesEmpty";
import TablePlaceholder from "../../../../../utilities/minitiatures/TablePlaceholder/TablePlaceholder";
import ScrollEnd from "../../../../../utilities/minitiatures/ScrollEnd/ScrollEnd";
import { getArticles } from "../../../../../utilities/api/actions";
import arrayMerge from "../../../../../utilities/helpers/arrayMerge";
import "./Articles.scss";

const DEFAULT_EDIT = {
    current: null as Article | null,
    setCurrent: (article: Article | null) => { article }
}

const DEFAULT_DELETE = {
    current: null as Article[] | null,
    setCurrent: (articles: Article[] | null) => { articles }
}

const ArticlesContext = React.createContext({
    edit: DEFAULT_EDIT,
    onDelete: DEFAULT_DELETE,
});

export const useEditArticle = () => {
    return React.useContext(ArticlesContext).edit;
}

export const useDeleteArticle = () => {
    return React.useContext(ArticlesContext).onDelete;
}

const dataLimit = 20;

const Articles = React.memo(() => {

    const { categories, articles } = useSelector((state: Rootstate) => state.backoffice);
    const dispatch = useDispatch<AppDispatch>();

    const [state, setState] = React.useState({
        edit: DEFAULT_EDIT,
        onDelete: DEFAULT_DELETE,
    });

    const [query, setQuery] = React.useState({
        offset: articles ? articles.length : 0,
        scrollEnd: true,
    });

    const edit = React.useMemo(() => {
        const setCurrent = (article: Article | null) => {
            setState(s => ({ ...s, edit: { ...s.edit, current: article } }));
        }

        return {
            current: state.edit.current,
            setCurrent
        }
    }, [state.edit.current]);

    const onDelete = React.useMemo(() => {
        const setCurrent = (articles: Article[] | null) => {
            setState(s => ({ ...s, onDelete: { ...s.onDelete, current: articles } }));
        }

        return {
            current: state.onDelete.current,
            setCurrent,
        }
    }, [state.onDelete.current]);

    React.useEffect(() => {
        if (!categories) {
            dispatch(refreshCategories());
        }
    }, [categories, articles]);

    const handleScrollEnd = React.useCallback(() => {
        const newQuery = { ...query };
        getArticles()
            .then(response => {
                const freshArticles: Article[] = response.data.articles;

                if (freshArticles.length > 0) {
                    const newArticles = arrayMerge<Article>(articles || [], freshArticles);
                    newQuery.offset = newArticles.length;
                    dispatch(setArticles(newArticles));
                }

                if (freshArticles.length < dataLimit) {
                    newQuery.scrollEnd = false;
                }

                setQuery(newQuery);
            })
    }, [articles, query]);

    return <ArticlesContext.Provider value={{ edit, onDelete }}>
        <div className="articles-container">
            <Fade show={Boolean(articles && articles.length > 0)}>
                <ArticlesList />
            </Fade>
            <Fade show={Boolean(articles && articles.length === 0)}>
                <ArticlesEmpty />
            </Fade>
            <ScrollEnd show={query.scrollEnd} whileInView={handleScrollEnd}>
                <TablePlaceholder />
            </ScrollEnd>
            <AddArticle />
            <DeleteArticle />
            <EditArticle />
        </div>
    </ArticlesContext.Provider>
});

export default Articles;
