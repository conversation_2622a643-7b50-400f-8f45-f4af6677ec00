import React from "react";
import { Modal } from "react-bootstrap";
import { useEditArticle } from "../Articles";
import EditArticleBody from "./EditArticleBody/EditArticleBody";

const EditArticle = React.memo(() => {
    const { current, setCurrent } = useEditArticle();

    const handleClose = React.useCallback(() => {
        setCurrent(null);
    }, [setCurrent]);

    return (
        <Modal 
            show={Boolean(current)} 
            onHide={handleClose} 
            size="xl" 
            centered
            className="edit-article-modal"
        >
            <EditArticleBody setShow={handleClose} />
        </Modal>
    );
});

export default EditArticle;
