import * as React from "react";
import { Modal } from "react-bootstrap";
import AddArticleBody from "./AddArticleBody/AddArticleBody";
import Button from "../../../../../../utilities/minitiatures/Button/Button";

const AddArticle = React.memo(() => {
    const [show, setShow] = React.useState(false);

    const handleShow = React.useCallback(() => {
        setShow(true);
    }, []);

    const handleClose = React.useCallback(() => {
        setShow(false);
    }, []);

    return <>
        <div className="d-flex justify-content-between align-items-center mb-4">
            <h3>Gestion des Articles</h3>
            <Button
                type="button"
                className="btn btn-primary"
                onClick={handleShow}>
                <i className="fa fa-plus"></i> Ajouter un article
            </Button>
        </div>

        <Modal
            show={show}
            onHide={handleClose}
            size="xl"
            centered
            className="add-article-modal"
        >
            <AddArticleBody setShow={setShow} />
        </Modal>
    </>
});

export default AddArticle;
