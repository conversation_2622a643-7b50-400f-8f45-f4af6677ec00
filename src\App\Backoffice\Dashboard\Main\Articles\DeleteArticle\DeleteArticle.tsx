import React from "react";
import { Modal } from "react-bootstrap";
import { useDeleteArticle } from "../Articles";
import Button from "../../../../../../utilities/minitiatures/Button/Button";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../../../../utilities/redux/store";
import { refreshArticles } from "../../../../../../utilities/redux/backoffice/backofficeSlice";
import useToasts from "../../../../../../utilities/minitiatures/Toast/hooks/useToasts";
import { deleteArticle } from "../../../../../../utilities/api/actions";

const DeleteArticle = React.memo(() => {
    const { current, setCurrent } = useDeleteArticle();
    const dispatch = useDispatch<AppDispatch>();
    const toasts = useToasts();

    const [loading, setLoading] = React.useState(false);

    const handleClose = React.useCallback(() => {
        setCurrent(null);
    }, [setCurrent]);

    const handleDelete = React.useCallback(() => {
        if (!current) return;

        setLoading(true);
        const ids = current.map(article => article.id);

        deleteArticle(ids)
            .then(() => {
                toasts.push({
                    title: "Articles supprimés",
                    content: `${current.length} article(s) supprimé(s) avec succès`,
                    type: "success",
                });
                setCurrent(null);
                dispatch(refreshArticles());
            })
            .catch(() => {
                toasts.push({
                    title: "Erreur",
                    content: "Une erreur s'est produite lors de la suppression",
                    type: "danger",
                });
            })
            .finally(() => {
                setLoading(false);
            });
    }, [current, setCurrent, dispatch, toasts.push]);

    return (
        <Modal show={Boolean(current)} onHide={handleClose} centered>
            <Modal.Header closeButton>
                <Modal.Title>Supprimer les articles</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <p>
                    Êtes-vous sûr de vouloir supprimer {current?.length || 0} article(s) ?
                    Cette action est irréversible.
                </p>
                {current && current.length > 0 && (
                    <ul className="list-unstyled">
                        {current.map(article => (
                            <li key={article.id} className="mb-1">
                                <strong>{article.title}</strong> par {article.author}
                            </li>
                        ))}
                    </ul>
                )}
            </Modal.Body>
            <Modal.Footer>
                <Button
                    type="button"
                    className="btn btn-outline-secondary"
                    onClick={handleClose}
                >
                    Annuler
                </Button>
                <Button
                    type="button"
                    className="btn btn-danger"
                    onClick={handleDelete}
                    options={{ loading }}
                >
                    <i className="fa fa-trash"></i> Supprimer
                </Button>
            </Modal.Footer>
        </Modal>
    );
});

export default DeleteArticle;
