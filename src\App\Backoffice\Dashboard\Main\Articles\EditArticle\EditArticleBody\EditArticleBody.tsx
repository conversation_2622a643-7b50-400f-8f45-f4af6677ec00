import React from "react";
import { Modal } from "react-bootstrap";
import Input from "../../../../../../../utilities/minitiatures/Input/Input";
import SelectedCategory from "../../../Categories/AddCategory/SelectedCategory/SelectedCategory";
import Button from "../../../../../../../utilities/minitiatures/Button/Button";
import useCategorySelect from "../../../../../../../utilities/minitiatures/CategorySelect/hooks/useCategorySelect";
import { Category, Article } from "../../../../../../../utilities/constants/types";
import { useEditArticle } from "../../Articles";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, Rootstate } from "../../../../../../../utilities/redux/store";
import { refreshArticles } from "../../../../../../../utilities/redux/backoffice/backofficeSlice";
import useToasts from "../../../../../../../utilities/minitiatures/Toast/hooks/useToasts";
import { updateArticle } from "../../../../../../../utilities/api/actions";
import { AxiosError } from "axios";
import truthyEntriesOnly from "../../../../../../../utilities/helpers/truthyEntriesOnly";
import ArticleContent from "../../AddArticle/AddArticleBody/ArticleContent";
import ArticleImages from "../../AddArticle/AddArticleBody/ArticleImages";

type Props = {
    setShow: (show: boolean) => void
}

type Edits = {
    title: string,
    author: string,
    category_id: number | null,
}

const EditArticleBody = React.memo((props: Props) => {
    const { current } = useEditArticle();
    const toasts = useToasts();
    const categorySelect = useCategorySelect();
    const dispatch = useDispatch<AppDispatch>();
    const categories = useSelector((state: Rootstate) => state.backoffice.categories);

    const [state, setState] = React.useState({
        edits: {
            title: current?.title || '',
            author: current?.author || '',
            category_id: current?.category?.id || null,
        } as Edits,
        loading: false,
        validationMessages: null as null | { [key: string]: any },
        sections: current?.sections || [],
        images: current?.images || [],
        selectedCategory: current?.category || null as Category | null,
    });

    const HandleCategorySelectClose = React.useCallback((selected: Category | null) => {
        setState(s => ({ 
            ...s, 
            selectedCategory: selected,
            edits: { ...s.edits, category_id: selected?.id || null }
        }));
    }, []);

    const handleOpenCategorySelect = React.useCallback(() => {
        categorySelect.open(HandleCategorySelectClose, state.selectedCategory?.id);
    }, [categorySelect.open, HandleCategorySelectClose, state.selectedCategory]);

    const handleChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setState(s => ({ ...s, edits: { ...s.edits, [name]: value } }));
    }, []);

    const handleSectionsChange = React.useCallback((sections: any[]) => {
        setState(s => ({ ...s, sections }));
    }, []);

    const handleImagesChange = React.useCallback((images: any[]) => {
        setState(s => ({ ...s, images }));
    }, []);

    const handleSubmit = React.useCallback(() => {
        if (!current) return;

        setState(s => ({ ...s, loading: true }));

        const payload = truthyEntriesOnly({
            title: state.edits.title,
            author: state.edits.author,
            category_id: state.edits.category_id,
            sections: state.sections.length > 0 ? state.sections : undefined,
            images: state.images.length > 0 ? state.images.map((img: any) => img.imageData || img) : undefined,
        }) || {};

        updateArticle(current.id, payload)
            .then(() => {
                props.setShow(false);
                toasts.push({
                    title: "Article mis à jour!",
                    content: "L'article a été mis à jour avec succès",
                    type: "success",
                });
                dispatch(refreshArticles());
            })
            .catch((error: AxiosError) => {
                const { errors } = error.response?.data as { errors: null };
                if (errors) {
                    setState(s => ({ ...s, validationMessages: errors }));
                } else {
                    toasts.push({
                        title: "Impossible de mettre à jour l'article",
                        content: "Une erreur a été rencontrée lors de la mise à jour de l'article.",
                        type: "danger",
                    });
                }
            })
            .finally(() => {
                setState(s => ({ ...s, loading: false }));
            });
    }, [current, state, props.setShow, toasts.push, dispatch]);

    return <>
        <Modal.Header closeButton>
            <Modal.Title>
                Modifier l'article
            </Modal.Title>
        </Modal.Header>
        <Modal.Body className="d-flex flex-wrap justify-content-between add-article-modal-body px-5">
            <div className="col-5 my-3">
                <label htmlFor="article-title">Titre de l'article *</label>
                <Input
                    type="text"
                    placeholder="Le titre de votre article"
                    name="title"
                    id="article-title"
                    onChange={handleChange}
                    value={state.edits.title}
                    options={{ error: state.validationMessages?.title }} />
            </div>
            <div className="col-5 my-3">
                <label htmlFor="article-author">Auteur de l'article *</label>
                <Input
                    type="text"
                    placeholder="L'auteur de l'article"
                    name="author"
                    id="article-author"
                    onChange={handleChange}
                    value={state.edits.author}
                    options={{ error: state.validationMessages?.author }} />
            </div>
            <div className="col-5 my-3 d-flex justify-content-between">
                <div>
                    <h6>Catégorie de l'article *</h6>
                    <SelectedCategory category={state.selectedCategory} />
                </div>
                <Button
                    type="button"
                    className="btn btn-outline-dark btn-sm align-self-start"
                    onClick={handleOpenCategorySelect}>Ouvrir <i className="fa fa-external-link"></i></Button>
            </div>
            <div className="col-5 my-3">
                <h6>Images de l'article</h6>
                <ArticleImages 
                    images={state.images}
                    onChange={handleImagesChange}
                />
            </div>
            <div className="col-12 my-3">
                <h6>Contenu de l'article</h6>
                <ArticleContent 
                    sections={state.sections}
                    onChange={handleSectionsChange}
                />
            </div>
        </Modal.Body>
        <Modal.Footer>
            <Button type="button" className="btn btn-outline-dark btn-sm" onClick={() => props.setShow(false)}>Annuler</Button>
            <Button
                type="button"
                className="btn btn-primary"
                onClick={handleSubmit}
                options={{ loading: state.loading }}>
                <i className="fa fa-check"></i> Mettre à jour
            </Button>
        </Modal.Footer>
    </>
});

export default EditArticleBody;
