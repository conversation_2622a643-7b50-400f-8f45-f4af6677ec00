import React from "react";
import { Article } from "../../../../../../../../utilities/constants/types";
import { useEditArticle, useDeleteArticle } from "../../Articles";
import { Dropdown } from "react-bootstrap";
import toFormatedDateString from "../../../../../../../../utilities/helpers/toFormatedDateString";

type Props = {
    article: Article;
}

const ArticleRow = React.memo((props: Props) => {
    const { article } = props;
    const { setCurrent: setEditCurrent } = useEditArticle();
    const { setCurrent: setDeleteCurrent } = useDeleteArticle();

    const [selected, setSelected] = React.useState(false);

    const handleEdit = React.useCallback(() => {
        setEditCurrent(article);
    }, [article, setEditCurrent]);

    const handleDelete = React.useCallback(() => {
        setDeleteCurrent([article]);
    }, [article, setDeleteCurrent]);

    const handleSelect = React.useCallback(() => {
        setSelected(!selected);
    }, [selected]);

    return <tr className={`article-row ${selected ? 'table-active' : ''}`}>
        <td>
            <input
                type="checkbox"
                className="form-check-input"
                checked={selected}
                onChange={handleSelect}
            />
        </td>
        <td className="article-row-title">
            <strong>{article.title}</strong>
        </td>
        <td className="article-row-author">
            {article.author}
        </td>
        <td className="article-row-category">
            {article.category?.name || 'Non catégorisé'}
        </td>
        <td className="article-row-created">
            {toFormatedDateString(article.created_at)}
        </td>
        <td>
            <Dropdown className="actions-dropdown">
                <Dropdown.Toggle variant="" className="btn btn-sm">
                    <i className="fa fa-ellipsis-v"></i>
                </Dropdown.Toggle>
                <Dropdown.Menu>
                    <Dropdown.Item onClick={handleEdit}>
                        <i className="fa fa-pencil"></i> Modifier
                    </Dropdown.Item>
                    <Dropdown.Item
                        className="text-danger"
                        onClick={handleDelete}>
                        <i className="fa fa-trash"></i> Supprimer
                    </Dropdown.Item>
                </Dropdown.Menu>
            </Dropdown>
        </td>
    </tr>
});

export default ArticleRow; 