import * as React from "react";
import Button from "../../../../../../../utilities/minitiatures/Button/Button";
import { Section, Subsection, Paragraph } from "../../../../../../../utilities/constants/types";

type Props = {
    sections: Section[],
    onChange: (sections: Section[]) => void,
}

const ArticleContent = React.memo((props: Props) => {
    const { sections, onChange } = props;

    const addSection = React.useCallback(() => {
        const newSection: Section = {
            id: Date.now(), // Temporary ID for new sections
            title: '',
            order: sections.length + 1,
            subsections: []
        };
        onChange([...sections, newSection]);
    }, [sections, onChange]);

    const updateSection = React.useCallback((sectionIndex: number, field: keyof Section, value: string | number) => {
        const newSections = [...sections];
        newSections[sectionIndex] = { ...newSections[sectionIndex], [field]: value };
        onChange(newSections);
    }, [sections, onChange]);

    const removeSection = React.useCallback((sectionIndex: number) => {
        const newSections = sections.filter((_, index) => index !== sectionIndex);
        onChange(newSections);
    }, [sections, onChange]);

    const addSubsection = React.useCallback((sectionIndex: number) => {
        const newSections = [...sections];
        const newSubsection: Subsection = {
            id: Date.now(),
            title: '',
            order: newSections[sectionIndex].subsections.length + 1,
            paragraphs: []
        };
        newSections[sectionIndex].subsections.push(newSubsection);
        onChange(newSections);
    }, [sections, onChange]);

    const updateSubsection = React.useCallback((sectionIndex: number, subsectionIndex: number, field: keyof Subsection, value: string | number) => {
        const newSections = [...sections];
        newSections[sectionIndex].subsections[subsectionIndex] = {
            ...newSections[sectionIndex].subsections[subsectionIndex],
            [field]: value
        };
        onChange(newSections);
    }, [sections, onChange]);

    const removeSubsection = React.useCallback((sectionIndex: number, subsectionIndex: number) => {
        const newSections = [...sections];
        newSections[sectionIndex].subsections = newSections[sectionIndex].subsections.filter((_, index) => index !== subsectionIndex);
        onChange(newSections);
    }, [sections, onChange]);

    const addParagraph = React.useCallback((sectionIndex: number, subsectionIndex: number) => {
        const newSections = [...sections];
        const newParagraph: Paragraph = {
            id: Date.now(),
            content: ''
        };
        newSections[sectionIndex].subsections[subsectionIndex].paragraphs.push(newParagraph);
        onChange(newSections);
    }, [sections, onChange]);

    const updateParagraph = React.useCallback((sectionIndex: number, subsectionIndex: number, paragraphIndex: number, content: string) => {
        const newSections = [...sections];
        newSections[sectionIndex].subsections[subsectionIndex].paragraphs[paragraphIndex] = {
            ...newSections[sectionIndex].subsections[subsectionIndex].paragraphs[paragraphIndex],
            content
        };
        onChange(newSections);
    }, [sections, onChange]);

    const removeParagraph = React.useCallback((sectionIndex: number, subsectionIndex: number, paragraphIndex: number) => {
        const newSections = [...sections];
        newSections[sectionIndex].subsections[subsectionIndex].paragraphs =
            newSections[sectionIndex].subsections[subsectionIndex].paragraphs.filter((_, index) => index !== paragraphIndex);
        onChange(newSections);
    }, [sections, onChange]);

    return (
        <div className="article-content">
            {sections.map((section, sectionIndex) => (
                <div key={section.id || sectionIndex} className="section-item border rounded p-3 mb-3">
                    <div className="d-flex justify-content-between align-items-center mb-2">
                        <h6 className="mb-0">Section {sectionIndex + 1}</h6>
                        <Button
                            type="button"
                            className="btn btn-outline-danger btn-sm"
                            onClick={() => removeSection(sectionIndex)}>
                            <i className="fa fa-trash"></i>
                        </Button>
                    </div>

                    <div className="mb-3">
                        <input
                            type="text"
                            className="form-control"
                            placeholder="Titre de la section"
                            value={section.title}
                            onChange={(e) => updateSection(sectionIndex, 'title', e.target.value)}
                        />
                    </div>

                    {/* Subsections */}
                    {section.subsections?.map((subsection: Subsection, subsectionIndex: number) => (
                        <div key={subsection.id || subsectionIndex} className="subsection-item border-start border-3 ps-3 mb-3">
                            <div className="d-flex justify-content-between align-items-center mb-2">
                                <small className="text-muted">Sous-section {subsectionIndex + 1}</small>
                                <Button
                                    type="button"
                                    className="btn btn-outline-danger btn-sm"
                                    onClick={() => removeSubsection(sectionIndex, subsectionIndex)}>
                                    <i className="fa fa-trash"></i>
                                </Button>
                            </div>

                            <div className="mb-2">
                                <input
                                    type="text"
                                    className="form-control form-control-sm"
                                    placeholder="Titre de la sous-section"
                                    value={subsection.title}
                                    onChange={(e) => updateSubsection(sectionIndex, subsectionIndex, 'title', e.target.value)}
                                />
                            </div>

                            {/* Paragraphs */}
                            {subsection.paragraphs?.map((paragraph: Paragraph, paragraphIndex: number) => (
                                <div key={paragraph.id || paragraphIndex} className="paragraph-item mb-2">
                                    <div className="d-flex gap-2">
                                        <textarea
                                            className="form-control form-control-sm"
                                            placeholder="Contenu du paragraphe"
                                            rows={3}
                                            value={paragraph.content}
                                            onChange={(e) => updateParagraph(sectionIndex, subsectionIndex, paragraphIndex, e.target.value)}
                                        />
                                        <Button
                                            type="button"
                                            className="btn btn-outline-danger btn-sm"
                                            onClick={() => removeParagraph(sectionIndex, subsectionIndex, paragraphIndex)}>
                                            <i className="fa fa-trash"></i>
                                        </Button>
                                    </div>
                                </div>
                            ))}

                            <Button
                                type="button"
                                className="btn btn-outline-primary btn-sm"
                                onClick={() => addParagraph(sectionIndex, subsectionIndex)}>
                                <i className="fa fa-plus"></i> Ajouter un paragraphe
                            </Button>
                        </div>
                    ))}

                    <Button
                        type="button"
                        className="btn btn-outline-secondary btn-sm"
                        onClick={() => addSubsection(sectionIndex)}>
                        <i className="fa fa-plus"></i> Ajouter une sous-section
                    </Button>
                </div>
            ))}

            <Button
                type="button"
                className="btn btn-primary"
                onClick={addSection}>
                <i className="fa fa-plus"></i> Ajouter une section
            </Button>
        </div>
    );
});

export default ArticleContent;