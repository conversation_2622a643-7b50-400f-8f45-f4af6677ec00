.articles-container {
  padding: 20px;

  .articles-list {
    .articles-list-title {
      color: #333;
      margin-bottom: 20px;
      font-weight: 600;
    }

    .article-row {
      transition: all 0.2s ease;

      &:hover {
        background-color: #f8f9fa;
      }

      .article-row-title {
        font-weight: 500;
        color: #2c3e50;
      }

      .article-row-author {
        color: #6c757d;
        font-style: italic;
      }

      .article-row-category {
        color: #495057;
        font-size: 0.9em;
      }

      .actions-dropdown {
        .dropdown-toggle {
          border: none;
          background: transparent;
          color: #6c757d;

          &:hover {
            color: #495057;
          }

          &::after {
            display: none;
          }
        }
      }
    }
  }
}

.add-article-modal {
  .add-article-modal-body {
    max-height: 70vh;
    overflow-y: auto;
  }

  .article-images-container {
    .images-captions {
      margin-top: 15px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      h6 {
        color: #495057;
        margin-bottom: 10px;
      }

      .form-label {
        color: #6c757d;
        font-weight: 500;
      }
    }
  }

  .article-content {
    .section-item {
      background-color: #f8f9fa;
      border: 1px solid #e9ecef !important;

      .subsection-item {
        background-color: #ffffff;
        border-left: 3px solid #007bff !important;
        margin: 10px 0;
        padding: 15px;
        border-radius: 5px;

        .paragraph-item {
          margin-bottom: 10px;

          textarea {
            resize: vertical;
            min-height: 80px;
          }
        }
      }

      h6 {
        color: #495057;
        font-weight: 600;
      }

      .btn-outline-danger {
        border-color: #dc3545;
        color: #dc3545;

        &:hover {
          background-color: #dc3545;
          color: white;
        }
      }

      .btn-outline-primary {
        border-color: #007bff;
        color: #007bff;

        &:hover {
          background-color: #007bff;
          color: white;
        }
      }

      .btn-outline-secondary {
        border-color: #6c757d;
        color: #6c757d;

        &:hover {
          background-color: #6c757d;
          color: white;
        }
      }
    }
  }
}

.edit-article-modal {
  .modal-dialog {
    max-width: 90vw;
  }
}

// Responsive design
@media (max-width: 768px) {
  .articles-container {
    padding: 10px;

    .articles-list {
      .table {
        font-size: 0.9em;

        th, td {
          padding: 8px 4px;
        }

        .article-row-title {
          font-size: 0.9em;
        }

        .article-row-author,
        .article-row-category {
          font-size: 0.8em;
        }
      }
    }
  }

  .add-article-modal,
  .edit-article-modal {
    .modal-dialog {
      margin: 10px;
      max-width: calc(100vw - 20px);
    }

    .add-article-modal-body {
      padding: 15px !important;

      .col-5 {
        width: 100% !important;
        margin-bottom: 15px !important;
      }

      .col-12 {
        margin-bottom: 15px !important;
      }
    }

    .article-content {
      .section-item {
        padding: 10px;

        .subsection-item {
          padding: 10px;
          margin: 5px 0;
        }
      }
    }
  }
}
