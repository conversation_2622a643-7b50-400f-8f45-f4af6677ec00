import React from "react";
import { useSelector } from "react-redux";
import { Rootstate } from "../../../../../../utilities/redux/store";
import ArticleRow from "./ArticleRow/ArticleRow";

const ArticlesList = React.memo(() => {
    const { articles } = useSelector((state: Rootstate) => state.backoffice);

    return <div className="articles-list">
        <h4 className="articles-list-title">Liste des articles</h4>

        {(articles && articles.length > 0) &&
            <table className="table table-striped table-hover align-middle table-borderless">
                <thead>
                    <tr>
                        <th scope="col">
                            <input type="checkbox" className="form-check-input" />
                        </th>
                        <th scope="col" className="col-3">Titre</th>
                        <th scope="col" className="col-2">Auteur</th>
                        <th scope="col" className="col-2">Catégorie</th>
                        <th scope="col" className="col-2">Date de création</th>
                        <th scope="col" className="col-1">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {articles.map(article => {
                        return <ArticleRow
                            article={article}
                            key={article.id} />
                    })}
                </tbody>
            </table>}
    </div>
});

export default ArticlesList;
